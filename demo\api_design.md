# 后端API接口设计

## 获取条目列表接口

**接口路径**: `GET /api/items`

**请求参数**:
```json
{
  "category": "all|postcard|ticket",  // 分类筛选: 全部|明信片|车票
  "page": 1,                          // 页码
  "limit": 20                         // 每页数量
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "id": "1",
        "title": "条目标题",
        "imageUrl": "https://example.com/image.jpg",
        "category": "postcard",
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 10,
      "totalItems": 200,
      "hasNextPage": true
    }
  }
}
```

## 获取分类统计接口

**接口路径**: `GET /api/categories/stats`

**响应数据**:
```json
{
  "code": 200,
  "message": "success", 
  "data": {
    "all": 200,
    "postcard": 120,
    "ticket": 80
  }
}
```