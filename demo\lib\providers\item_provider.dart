// providers/item_provider.dart
// 条目数据的状态管理

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/item_model.dart';
import '../models/category_enum.dart';
import '../models/pagination_model.dart';
import '../services/api_service.dart';

// 当前选中的分类Provider
final selectedCategoryProvider = StateProvider<ItemCategory>((ref) {
  return ItemCategory.all;
});

// 条目列表状态类
class ItemListState {
  final List<ItemModel> items;
  final PaginationModel? pagination;
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;

  const ItemListState({
    this.items = const [],
    this.pagination,
    this.isLoading = false,
    this.isLoadingMore = false,
    this.error,
  });

  ItemListState copyWith({
    List<ItemModel>? items,
    PaginationModel? pagination,
    bool? isLoading,
    bool? isLoadingMore,
    String? error,
  }) {
    return ItemListState(
      items: items ?? this.items,
      pagination: pagination ?? this.pagination,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      error: error ?? this.error,
    );
  }
}

// 条目列表Provider
class ItemListNotifier extends StateNotifier<ItemListState> {
  ItemListNotifier(this._apiService) : super(const ItemListState());

  final ApiService _apiService;
  int _currentPage = 1;

  // 加载第一页数据
  Future<void> loadItems(ItemCategory category) async {
    state = state.copyWith(isLoading: true, error: null);
    _currentPage = 1;

    try {
      final response = await _apiService.getItems(
        category: category.value,
        page: _currentPage,
        limit: 20,
      );

      state = state.copyWith(
        items: response.items,
        pagination: response.pagination,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  // 加载更多数据
  Future<void> loadMoreItems(ItemCategory category) async {
    if (state.isLoadingMore || 
        state.pagination?.hasNextPage != true) return;

    state = state.copyWith(isLoadingMore: true);
    _currentPage++;

    try {
      final response = await _apiService.getItems(
        category: category.value,
        page: _currentPage,
        limit: 20,
      );

      state = state.copyWith(
        items: [...state.items, ...response.items],
        pagination: response.pagination,
        isLoadingMore: false,
      );
    } catch (e) {
      _currentPage--; // 回滚页码
      state = state.copyWith(
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  // 刷新数据
  Future<void> refreshItems(ItemCategory category) async {
    await loadItems(category);
  }
}

// 条目列表Provider实例
final itemListProvider = StateNotifierProvider<ItemListNotifier, ItemListState>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return ItemListNotifier(apiService);
});

// 监听分类变化并自动加载数据的Provider
final itemListWithCategoryProvider = Provider<ItemListState>((ref) {
  final category = ref.watch(selectedCategoryProvider);
  final itemListNotifier = ref.watch(itemListProvider.notifier);
  
  // 当分类改变时，重新加载数据
  ref.listen(selectedCategoryProvider, (prev, next) {
    if (prev != next) {
      itemListNotifier.loadItems(next);
    }
  });

  return ref.watch(itemListProvider);
});