// widgets/item_grid_list.dart
// 三列网格列表组件

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/item_model.dart';
import '../providers/item_provider.dart';

class ItemGridList extends ConsumerStatefulWidget {
  const ItemGridList({super.key});

  @override
  ConsumerState<ItemGridList> createState() => _ItemGridListState();
}

class _ItemGridListState extends ConsumerState<ItemGridList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 监听滚动事件，实现上拉加载
    _scrollController.addListener(_onScroll);
    
    // 初始化加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final category = ref.read(selectedCategoryProvider);
      ref.read(itemListProvider.notifier).loadItems(category);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 滚动监听方法
  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      // 距离底部200px时开始加载更多
      final category = ref.read(selectedCategoryProvider);
      ref.read(itemListProvider.notifier).loadMoreItems(category);
    }
  }

  @override
  Widget build(BuildContext context) {
    final itemListState = ref.watch(itemListProvider);
    final selectedCategory = ref.watch(selectedCategoryProvider);

    // 错误状态
    if (itemListState.error != null && itemListState.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              itemListState.error!,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(itemListProvider.notifier).refreshItems(selectedCategory);
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    // 初始加载状态
    if (itemListState.isLoading && itemListState.items.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // 空数据状态
    if (itemListState.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              '暂无数据',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(itemListProvider.notifier).refreshItems(selectedCategory);
      },
      child: GridView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,           // 三列布局
          crossAxisSpacing: 12,        // 列间距
          mainAxisSpacing: 16,         // 行间距
          childAspectRatio: 0.8,       // 宽高比，调整卡片形状
        ),
        itemCount: itemListState.items.length + 
                  (itemListState.isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          // 加载更多指示器
          if (index == itemListState.items.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final item = itemListState.items[index];
          return ItemCard(item: item);
        },
      ),
    );
  }
}

// 单个条目卡片组件
class ItemCard extends StatelessWidget {
  final ItemModel item;

  const ItemCard({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 图片区域
        Expanded(
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey[200],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: CachedNetworkImage(
                imageUrl: item.imageUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[200],
                  child: Center(
                    child: Icon(
                      Icons.image_outlined,
                      size: 32,
                      color: Colors.grey[400],
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[200],
                  child: Center(
                    child: Icon(
                      Icons.broken_image_outlined,
                      size: 32,
                      color: Colors.grey[400],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        // 标题区域
        Text(
          item.title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}