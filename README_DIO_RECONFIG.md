# Dio重新配置功能

## 概述

为小鸡旅行应用的Dio HTTP客户端添加了强大的重新配置功能，支持运行时动态调整网络配置。

## 新增功能

### 1. 核心组件

- **DioConfig**: 配置数据类，包含所有Dio相关设置
- **DioConfigNotifier**: 状态管理类，负责配置的更新和维护
- **DioReconfigHelper**: 工具类，提供便捷的配置操作方法
- **ApiService**: API服务类，自动使用最新的Dio配置

### 2. 主要特性

✅ **动态配置更新**: 运行时修改API端点、超时设置等
✅ **环境切换**: 轻松在开发、测试、生产环境间切换
✅ **认证管理**: 动态添加/移除认证令牌
✅ **日志控制**: 运行时启用/禁用网络日志
✅ **网络适配**: 根据网络状况调整超时设置
✅ **配置重置**: 一键恢复默认配置

### 3. 新增页面

- **设置页面** (`lib/pages/settings_page.dart`): 应用设置入口
- **Dio配置页面** (`lib/pages/dio_config_page.dart`): 网络配置管理界面
- **功能演示页面** (`lib/examples/dio_reconfig_examples.dart`): 重新配置功能演示

## 快速开始

### 基本使用

```dart
// 在Widget中使用
class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听配置变化
    final config = ref.watch(dioConfigProvider);
    
    return Column(
      children: [
        Text('当前API: ${config.baseUrl}'),
        ElevatedButton(
          onPressed: () {
            // 更新基础URL
            DioReconfigHelper.updateBaseUrl(ref, 'https://new-api.com');
          },
          child: Text('切换API'),
        ),
      ],
    );
  }
}
```

### 常用操作

```dart
// 切换环境
DioReconfigHelper.updateBaseUrl(ref, 'https://api.production.com');

// 添加认证
DioReconfigHelper.addAuthToken(ref, 'your_token_here');

// 调整超时
DioReconfigHelper.updateTimeouts(
  ref,
  connectTimeout: Duration(seconds: 30),
  receiveTimeout: Duration(seconds: 60),
);

// 启用日志
DioReconfigHelper.enableLogging(ref);

// 重置配置
ref.read(dioConfigProvider.notifier).resetToDefault();
```

## 文件结构

```
lib/
├── providers/
│   └── dio_provider.dart          # Dio配置Provider和工具类
├── services/
│   └── api_service.dart           # API服务类
├── pages/
│   ├── settings_page.dart         # 设置页面
│   └── dio_config_page.dart       # Dio配置页面
├── examples/
│   └── dio_reconfig_examples.dart # 使用示例
└── models/
    └── pagination_model.dart      # 分页模型

docs/
└── dio_reconfig_guide.md          # 详细使用指南

test/
└── dio_reconfig_test.dart          # 单元测试
```

## 访问方式

1. 启动应用
2. 点击底部导航栏的"设置"图标（最右侧）
3. 选择"网络配置"进入Dio配置页面
4. 或选择"Dio功能演示"查看功能演示

## 配置选项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| baseUrl | API基础地址 | https://example.com |
| connectTimeout | 连接超时时间 | 10秒 |
| receiveTimeout | 接收超时时间 | 10秒 |
| sendTimeout | 发送超时时间 | 10秒 |
| headers | 请求头 | 空 |
| enableLogging | 是否启用日志 | false |

## 使用场景

### 1. 开发调试
- 在开发环境启用详细日志
- 切换到本地测试服务器
- 调整超时时间便于调试

### 2. 环境部署
- 生产环境禁用日志记录
- 根据环境切换API端点
- 配置生产环境的超时设置

### 3. 用户体验
- 网络不佳时自动调整超时
- 用户登录后自动添加认证
- 登出时清除敏感信息

### 4. 运维监控
- 动态调整日志级别
- 临时切换备用服务器
- 快速回滚配置更改

## 测试

运行单元测试验证功能：

```bash
flutter test test/dio_reconfig_test.dart
```

## 注意事项

1. **Provider依赖**: 确保在ProviderScope内使用
2. **配置持久化**: 当前配置不会自动保存，重启应用会恢复默认值
3. **线程安全**: 所有配置操作都是线程安全的
4. **性能影响**: 配置变更会重新创建Dio实例

## 扩展建议

- 添加配置预设功能
- 实现配置的本地持久化
- 添加配置变更历史记录
- 集成网络状况自动检测
- 支持多个Dio实例管理

## 技术栈

- Flutter 3.x
- Riverpod 2.6.1
- Dio 5.8.0
- Material Design 3
