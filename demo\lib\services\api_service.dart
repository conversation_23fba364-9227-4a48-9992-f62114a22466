// services/api_service.dart
// API服务类

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import '../models/item_model.dart';
import '../models/pagination_model.dart';

class ApiResponse {
  final List<ItemModel> items;
  final PaginationModel pagination;

  const ApiResponse({
    required this.items,
    required this.pagination,
  });
}

class ApiService {
  final Dio _dio;

  ApiService(this._dio);

  // 获取条目列表
  Future<ApiResponse> getItems({
    required String category,
    required int page,
    required int limit,
  }) async {
    try {
      final response = await _dio.get('/api/items', queryParameters: {
        'category': category,
        'page': page,
        'limit': limit,
      });

      final data = response.data['data'] as Map<String, dynamic>;
      
      final items = (data['items'] as List)
          .map((json) => ItemModel.fromJson(json as Map<String, dynamic>))
          .toList();

      final pagination = PaginationModel.fromJson(
        data['pagination'] as Map<String, dynamic>,
      );

      return ApiResponse(items: items, pagination: pagination);
    } catch (e) {
      throw Exception('加载数据失败: $e');
    }
  }

  // 获取分类统计（可选功能）
  Future<Map<String, int>> getCategoryStats() async {
    try {
      final response = await _dio.get('/api/categories/stats');
      return Map<String, int>.from(response.data['data'] as Map);
    } catch (e) {
      throw Exception('加载统计数据失败: $e');
    }
  }
}

// Dio实例Provider
final dioProvider = Provider<Dio>((ref) {
  final dio = Dio();
  dio.options.baseUrl = 'https://your-api-domain.com'; // 替换为实际API地址
  dio.options.connectTimeout = const Duration(seconds: 10);
  dio.options.receiveTimeout = const Duration(seconds: 10);
  
  // 添加请求/响应拦截器（可选）
  dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
  ));
  
  return dio;
});

// API服务Provider
final apiServiceProvider = Provider<ApiService>((ref) {
  final dio = ref.watch(dioProvider);
  return ApiService(dio);
});