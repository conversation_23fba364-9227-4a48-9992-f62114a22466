// models/item_model.dart
// 条目数据模型

class ItemModel {
  final String id;
  final String title;
  final String imageUrl;
  final String category;
  final DateTime createdAt;

  const ItemModel({
    required this.id,
    required this.title,
    required this.imageUrl,
    required this.category,
    required this.createdAt,
  });

  // 从JSON创建对象
  factory ItemModel.fromJson(Map<String, dynamic> json) {
    return ItemModel(
      id: json['id'] as String,
      title: json['title'] as String,
      imageUrl: json['imageUrl'] as String,
      category: json['category'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  // 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'imageUrl': imageUrl,
      'category': category,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ItemModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}