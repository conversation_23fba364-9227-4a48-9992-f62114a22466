{"openapi": "3.1.0", "info": {"title": "小鸡旅行", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/user/login": {"post": {"summary": "用户登录", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"credential_type": {"type": "string", "title": "凭据类型", "description": "目前支持\"password\""}, "credential_identifier": {"type": "string", "title": "凭据标识符"}, "credential_secret": {"type": ["string", "null"], "title": "凭据携带的密文"}}, "required": ["credential_type", "credential_identifier"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"$ref": "#/components/schemas/%E7%94%A8%E6%88%B7%E4%BF%A1%E6%81%AF%E6%95%B0%E6%8D%AE%E6%A8%A1%E5%9E%8B"}}, "required": ["token", "user"]}}, "required": ["code", "message", "data"]}}}, "headers": {}}}, "security": []}}, "/user/register": {"post": {"summary": "用户注册", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"nickname": {"type": "string", "title": "用户昵称"}, "avatar": {"type": ["string", "null"], "title": "用户头像URL"}, "credential_type": {"type": "string", "title": "注册使用的凭据类型"}, "credential_identifier": {"type": "string", "title": "注册使用的凭据标识符"}, "credential_secret": {"type": ["string", "null"], "title": "注册使用的凭据密文"}}, "required": ["nickname", "credential_type", "credential_identifier"]}, "example": {"nickname": "demo", "avatar": "", "credential_type": "password", "credential_identifier": "account", "credential_secret": "123456"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"$ref": "#/components/schemas/%E7%94%A8%E6%88%B7%E4%BF%A1%E6%81%AF%E6%95%B0%E6%8D%AE%E6%A8%A1%E5%9E%8B"}}, "required": ["token", "user"]}}, "required": ["code", "message"]}}}, "headers": {}}}, "security": []}}, "/user/update": {"post": {"summary": "修改用户信息", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"nickname": {"type": "string"}, "avatar": {"type": "string"}}, "required": ["nickname", "avatar"]}, "example": {"nickname": "test", "avatar": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"$ref": "#/components/schemas/%E7%94%A8%E6%88%B7%E4%BF%A1%E6%81%AF%E6%95%B0%E6%8D%AE%E6%A8%A1%E5%9E%8B"}}, "required": ["token", "user"]}}, "required": ["code", "message", "data"]}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/souvenirs": {"get": {"summary": "查询奖励列表", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "category", "in": "query", "description": "分组", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer"}}, {"name": "limit", "in": "query", "description": "每页个数", "required": false, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/%E5%A5%96%E5%8A%B1%E5%88%97%E8%A1%A8%E6%95%B0%E6%8D%AE%E6%A8%A1%E5%9E%8B"}}, "pagination": {"$ref": "#/components/schemas/%E5%88%86%E9%A1%B5%E5%93%8D%E5%BA%94%E6%95%B0%E6%8D%AE%E6%A8%A1%E5%9E%8B"}}, "required": ["items", "pagination"]}}, "required": ["code", "message"]}}}, "headers": {}}}, "security": [{"bearer": []}]}}}, "webhooks": {}, "components": {"schemas": {"用户信息数据模型": {"type": "object", "properties": {"id": {"type": "integer", "title": "主键ID"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}, "uid": {"type": "string", "title": "唯一用户ID", "description": "类似微信号，不允许重复"}, "nickname": {"type": "string", "title": "用户昵称", "description": "允许重复"}, "avatar": {"type": "string", "title": "用户头像URL"}}, "required": ["id", "created_at", "updated_at", "uid", "nickname", "avatar"]}, "奖励列表数据模型": {"type": "object", "properties": {"souvenir_id": {"type": "integer", "title": "奖励ID"}, "title": {"type": "string", "title": "标题"}, "img_url": {"type": "string", "title": "图片URL"}, "category": {"type": "string", "title": "奖励分类"}, "created_at": {"type": "string", "title": "创建时间"}}, "required": ["img_url", "souvenir_id", "category", "created_at", "title"]}, "分页响应数据模型": {"type": "object", "properties": {"current_page": {"type": "integer", "title": "当前页码"}, "total_pages": {"type": "integer", "title": "总页数"}, "total_items": {"type": "integer", "title": "总奖励数", "description": "分类下所有奖励的总数量"}, "has_next": {"type": "boolean", "title": "是否有下一页"}}, "required": ["current_page", "total_pages", "total_items", "has_next"], "title": ""}}, "securitySchemes": {"bearer": {"type": "http", "scheme": "bearer"}}}, "servers": [], "security": []}