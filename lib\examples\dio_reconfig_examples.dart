import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/dio_provider.dart';
import '../services/api_service.dart';

/// Dio重新配置使用示例
class DioReconfigExamples {
  
  /// 示例1: 切换API环境
  static void switchEnvironment(WidgetRef ref, String environment) {
    String baseUrl;
    
    switch (environment) {
      case 'development':
        baseUrl = 'https://dev-api.example.com';
        break;
      case 'staging':
        baseUrl = 'https://staging-api.example.com';
        break;
      case 'production':
        baseUrl = 'https://api.example.com';
        break;
      default:
        baseUrl = 'https://api.example.com';
    }
    
    // 更新基础URL
    DioReconfigHelper.updateBaseUrl(ref, baseUrl);
    
    // 根据环境启用或禁用日志
    if (environment == 'development') {
      DioReconfigHelper.enableLogging(ref);
    } else {
      DioReconfigHelper.disableLogging(ref);
    }
  }
  
  /// 示例2: 用户登录后添加认证令牌
  static void onUserLogin(WidgetRef ref, String authToken) {
    DioReconfigHelper.addAuthToken(ref, authToken);
  }
  
  /// 示例3: 用户登出后移除认证令牌
  static void onUserLogout(WidgetRef ref) {
    DioReconfigHelper.removeAuthToken(ref);
  }
  
  /// 示例4: 网络状况不佳时调整超时设置
  static void adjustForSlowNetwork(WidgetRef ref) {
    DioReconfigHelper.updateTimeouts(
      ref,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 60),
      sendTimeout: const Duration(seconds: 30),
    );
  }
  
  /// 示例5: 恢复正常网络超时设置
  static void restoreNormalTimeouts(WidgetRef ref) {
    DioReconfigHelper.updateTimeouts(
      ref,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 10),
    );
  }
  
  /// 示例6: 完整的配置更新
  static void updateCompleteConfig(WidgetRef ref, {
    required String baseUrl,
    required String authToken,
    required bool enableLogging,
    Duration? connectTimeout,
    Duration? receiveTimeout,
  }) {
    final newConfig = DioConfig(
      baseUrl: baseUrl,
      connectTimeout: connectTimeout ?? const Duration(seconds: 10),
      receiveTimeout: receiveTimeout ?? const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 10),
      headers: {'Authorization': 'Bearer $authToken'},
      enableLogging: enableLogging,
    );
    
    DioReconfigHelper.reconfigureDio(ref, newConfig);
  }
}

/// 演示Widget - 展示如何在实际组件中使用重新配置功能
class DioReconfigDemo extends ConsumerWidget {
  const DioReconfigDemo({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final config = ref.watch(dioConfigProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dio重新配置演示'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 当前配置信息
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前Dio配置',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text('URL: ${config.baseUrl}'),
                    Text('连接超时: ${config.connectTimeout.inSeconds}s'),
                    Text('接收超时: ${config.receiveTimeout.inSeconds}s'),
                    Text('日志: ${config.enableLogging ? "开启" : "关闭"}'),
                    if (config.headers.isNotEmpty)
                      Text('请求头: ${config.headers.keys.join(", ")}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // 环境切换按钮
            Text(
              '环境切换',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton(
                  onPressed: () => DioReconfigExamples.switchEnvironment(ref, 'development'),
                  child: const Text('开发环境'),
                ),
                ElevatedButton(
                  onPressed: () => DioReconfigExamples.switchEnvironment(ref, 'staging'),
                  child: const Text('测试环境'),
                ),
                ElevatedButton(
                  onPressed: () => DioReconfigExamples.switchEnvironment(ref, 'production'),
                  child: const Text('生产环境'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 认证操作
            Text(
              '认证操作',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                ElevatedButton(
                  onPressed: () => DioReconfigExamples.onUserLogin(ref, 'demo_token_123'),
                  child: const Text('模拟登录'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => DioReconfigExamples.onUserLogout(ref),
                  child: const Text('模拟登出'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 网络调整
            Text(
              '网络调整',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                ElevatedButton(
                  onPressed: () => DioReconfigExamples.adjustForSlowNetwork(ref),
                  child: const Text('慢网络模式'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => DioReconfigExamples.restoreNormalTimeouts(ref),
                  child: const Text('正常网络模式'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 测试API调用
            Text(
              'API测试',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => _testApiCall(ref, context),
              child: const Text('测试API调用'),
            ),
            const SizedBox(height: 16),
            
            // 重置配置
            ElevatedButton(
              onPressed: () => ref.read(dioConfigProvider.notifier).resetToDefault(),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('重置为默认配置'),
            ),
          ],
        ),
      ),
    );
  }
  
  void _testApiCall(WidgetRef ref, BuildContext context) async {
    try {
      final apiService = ref.read(apiServiceProvider);
      
      // 尝试获取条目列表
      final response = await apiService.getItems(
        category: 'all',
        page: 1,
        limit: 10,
      );
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('API调用成功，获取到${response.items.length}个条目'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('API调用失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
