import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../api/network/http_client.dart';
import '../api/services/auth_service.dart';
import '../models/user_model.dart';

/// 认证服务Provider
final authServiceProvider = Provider<AuthService>((ref) {
  final httpClient = ref.watch(httpClientProvider);
  return AuthService(httpClient);
});

/// 当前用户状态
class AuthState {
  final UserModel? user;
  final String? token;
  final bool isLoading;
  final String? error;

  const AuthState({this.user, this.token, this.isLoading = false, this.error});

  AuthState copyWith({UserModel? user, String? token, bool? isLoading, String? error}) {
    return AuthState(
      user: user ?? this.user,
      token: token ?? this.token,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  bool get isAuthenticated => user != null && token != null;

  @override
  String toString() {
    return 'AuthState(user: $user, isAuthenticated: $isAuthenticated, isLoading: $isLoading, error: $error)';
  }
}

/// 认证状态Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  final httpClient = ref.watch(httpClientProvider);
  return AuthNotifier(authService, httpClient);
});

/// 认证状态管理器
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;
  final HttpClient _httpClient;

  AuthNotifier(this._authService, this._httpClient) : super(const AuthState());

  /// 用户登录
  Future<void> login(String credentialIdentifier, String credentialSecret) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = LoginRequest(
        credentialType: 'password',
        credentialIdentifier: credentialIdentifier,
        credentialSecret: credentialSecret,
      );

      final response = await _authService.login(request);

      if (response.isSuccess && response.data != null) {
        final authData = response.data!;
        _httpClient.setToken(authData.token);

        state = state.copyWith(
          user: authData.user,
          token: authData.token,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 用户注册
  Future<void> register({
    required String nickname,
    required String credentialIdentifier,
    required String credentialSecret,
    String? avatar,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = RegisterRequest(
        nickname: nickname,
        avatar: avatar,
        credentialType: 'password',
        credentialIdentifier: credentialIdentifier,
        credentialSecret: credentialSecret,
      );

      final response = await _authService.register(request);

      if (response.isSuccess && response.data != null) {
        final authData = response.data!;
        _httpClient.setToken(authData.token);

        state = state.copyWith(
          user: authData.user,
          token: authData.token,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 更新用户信息
  Future<void> updateUser({required String nickname, required String avatar}) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = UserUpdateRequest(nickname: nickname, avatar: avatar);

      final response = await _authService.updateUser(request);

      if (response.isSuccess && response.data != null) {
        final authData = response.data!;
        _httpClient.setToken(authData.token);

        state = state.copyWith(
          user: authData.user,
          token: authData.token,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 登出
  void logout() {
    _httpClient.clearToken();
    state = const AuthState();
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}
