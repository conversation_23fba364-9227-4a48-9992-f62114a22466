/// 列表奖励项模型类
///
/// 用于列表中的奖励项
/// 包含ID、标题、图片URL、类别和创建时间
class SouvenirItem {
  // 奖励ID
  final String id;
  // 标题
  final String title;
  // 图片URL
  final String imageUrl;
  // 奖励类别
  final String category;
  // 创建时间
  final DateTime createdAt;

  const SouvenirItem({
    required this.id,
    required this.title,
    required this.imageUrl,
    required this.category,
    required this.createdAt,
  });

  // 从JSON创建对象
  factory SouvenirItem.fromJson(Map<String, dynamic> json) {
    return SouvenirItem(
      id: json['id'] as String,
      title: json['title'] as String,
      imageUrl: json['imageUrl'] as String,
      category: json['category'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  // 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'imageUrl': imageUrl,
      'category': category,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SouvenirItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
