# 小鸡旅行 API 配置文档

本文档说明如何使用基于 OpenAPI 规范配置的 Dio HTTP 客户端和 Riverpod 状态管理。

## 目录结构

```
lib/
├── api/
│   ├── network/
│   │   └── http_client.dart          # HTTP客户端配置
│   ├── services/
│   │   ├── auth_service.dart         # 认证服务
│   │   └── souvenir_service.dart     # 奖励服务
│   └── README.md                     # 本文档
├── models/
│   ├── api_response.dart             # 通用API响应模型
│   ├── user_model.dart               # 用户相关模型
│   ├── pagination_model.dart         # 分页模型
│   └── souvenir/
│       └── souvenir_item.dart        # 奖励项模型
├── providers/
│   ├── auth_provider.dart            # 认证状态管理
│   └── souvenir_provider.dart        # 奖励状态管理
└── examples/
    └── api_usage_example.dart        # 使用示例
```

## 核心功能

### 1. HTTP 客户端配置

`HttpClient` 类提供了完整的 HTTP 请求功能：

- **基础配置**: 基础URL、超时时间、请求头
- **认证支持**: Bearer Token 自动添加
- **错误处理**: 统一的错误处理和业务状态码检查
- **日志记录**: 请求和响应的详细日志

### 2. 认证功能

支持的认证操作：
- 用户登录 (`/user/login`)
- 用户注册 (`/user/register`)
- 更新用户信息 (`/user/update`)

### 3. 奖励功能

支持的奖励操作：
- 查询奖励列表 (`/souvenirs`)
- 分页加载
- 按分类筛选

## 使用方法

### 1. 基础设置

在 `main.dart` 中包装应用：

```dart
void main() {
  runApp(
    ProviderScope(
      child: MyApp(),
    ),
  );
}
```

### 2. 认证使用

```dart
class LoginPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    
    return Scaffold(
      body: Column(
        children: [
          // 显示认证状态
          if (authState.isAuthenticated)
            Text('欢迎, ${authState.user?.nickname}'),
          
          // 登录按钮
          ElevatedButton(
            onPressed: () async {
              await ref.read(authProvider.notifier).login(
                'username',
                'password',
              );
            },
            child: Text('登录'),
          ),
        ],
      ),
    );
  }
}
```

### 3. 奖励列表使用

```dart
class SouvenirListPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final souvenirState = ref.watch(defaultSouvenirListProvider);
    
    return Scaffold(
      body: ListView.builder(
        itemCount: souvenirState.items.length,
        itemBuilder: (context, index) {
          final item = souvenirState.items[index];
          return ListTile(
            title: Text(item.title),
            subtitle: Text(item.category),
            leading: Image.network(item.imgUrl),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          ref.read(defaultSouvenirListProvider.notifier).refresh();
        },
        child: Icon(Icons.refresh),
      ),
    );
  }
}
```

### 4. 分类筛选

```dart
// 获取特定分类的奖励
final categoryQuery = SouvenirQuery(category: 'electronics');
final categoryProvider = souvenirListProvider(categoryQuery);

// 在Widget中使用
final categoryState = ref.watch(categoryProvider);
```

## API 配置

### 修改基础URL

在 `lib/api/network/http_client.dart` 中修改：

```dart
static const String baseUrl = 'https://your-api-domain.com';
```

### 自定义超时时间

```dart
static const Duration connectTimeout = Duration(seconds: 30);
static const Duration receiveTimeout = Duration(seconds: 30);
static const Duration sendTimeout = Duration(seconds: 30);
```

## 错误处理

所有API调用都包含统一的错误处理：

- **网络错误**: 连接超时、网络不可用
- **HTTP错误**: 401认证失败、403权限不足、404资源不存在、500服务器错误
- **业务错误**: 基于API响应中的code字段

错误信息会自动设置到对应的Provider状态中，可以通过 `state.error` 获取。

## 状态管理

### AuthState

```dart
class AuthState {
  final UserModel? user;        // 当前用户信息
  final String? token;          // 认证token
  final bool isLoading;         // 加载状态
  final String? error;          // 错误信息
  
  bool get isAuthenticated;     // 是否已认证
}
```

### SouvenirListState

```dart
class SouvenirListState {
  final List<SouvenirItem> items;     // 奖励列表
  final PaginationModel? pagination;  // 分页信息
  final bool isLoading;               // 加载状态
  final String? error;                // 错误信息
  final bool hasMore;                 // 是否有更多数据
}
```

## 注意事项

1. **认证要求**: `/user/update` 和 `/souvenirs` 端点需要认证
2. **Token管理**: Token会自动添加到请求头，登出时会自动清除
3. **分页加载**: 支持无限滚动和手动加载更多
4. **错误恢复**: 提供清除错误状态的方法

## 列表页面使用

### 奖励列表页面

基于demo设计创建的完整列表页面，包含：

1. **分类筛选标签** (`CategoryTabs`)
   - 支持全部、明信片、车票等分类
   - 点击切换分类自动重新加载数据

2. **三列网格列表** (`SouvenirGridList`)
   - 三列网格布局展示奖励
   - 支持下拉刷新
   - 支持上拉加载更多
   - 自动处理加载状态和错误状态

### 使用方法

```dart
// 直接使用奖励列表页面
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const SouvenirListPage(),
  ),
);

// 或者在主页面中嵌入组件
class MyHomePage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Column(
        children: [
          CategoryTabs(),           // 分类筛选
          Divider(),
          Expanded(
            child: SouvenirGridList(), // 奖励列表
          ),
        ],
      ),
    );
  }
}
```

### 自定义分类

在 `lib/models/souvenir/category_enum.dart` 中添加新分类：

```dart
enum ItemCategory {
  all('all', '全部'),
  postcard('postcard', '明信片'),
  ticket('ticket', '车票'),
  newCategory('new_category', '新分类'), // 添加新分类
}
```

## 示例代码

- API使用示例: `lib/examples/api_usage_example.dart`
- 列表页面演示: `lib/examples/souvenir_list_demo.dart`
- 完整列表页面: `lib/pages/souvenir_list_page.dart`
