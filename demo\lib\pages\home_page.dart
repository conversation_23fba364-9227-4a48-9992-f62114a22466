// pages/home_page.dart
// 主页面，整合所有组件

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/category_tabs.dart';
import '../widgets/item_grid_list.dart';

class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('收藏'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        bottom: const PreferredSize(
          preferredSize: Size.fromHeight(1),
          child: Divider(height: 1, thickness: 1),
        ),
      ),
      backgroundColor: Colors.white,
      body: const Column(
        children: [
          // 分类筛选标签
          CategoryTabs(),
          // 分割线
          Divider(height: 1, thickness: 1),
          // 三列网格列表
          Expanded(
            child: ItemGridList(),
          ),
        ],
      ),
    );
  }
}